#!/usr/bin/env python3
"""
Test script to verify the planner_agent fix.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig

# Import the planner agent
from agents.bond_ai.nodes.planner_node import planner_agent
from agents.bond_ai.state import BondAIAgentState
from agents.bond_ai.configuration import Configuration

def test_planner_with_valid_state():
    """Test planner_agent with a valid state dictionary."""
    print("Testing planner_agent with valid state...")
    
    # Create a valid state
    state = {
        "messages": [
            HumanMessage(content="Create a marketing campaign for our new product launch")
        ],
        "plan_tasks": [],
        "table_summary": None,
        "mode": None,
        "selected_row_ids": None,
        "selected_column_ids": None,
        "intents": None,
        "tool_calls": None,
        "active_task_id": None
    }
    
    # Create a basic config
    config = RunnableConfig(
        configurable={
            "model": "openai/gpt-3.5-turbo",
            "temperature": 0.7
        }
    )
    
    try:
        result = planner_agent(state, config)
        print(f"✅ Success! Result type: {type(result)}")
        print(f"Result keys: {result.keys()}")
        if "plan_tasks" in result:
            print(f"Number of tasks created: {len(result['plan_tasks'])}")
        if "messages" in result:
            print(f"Number of messages: {len(result['messages'])}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_planner_with_invalid_state():
    """Test planner_agent with an invalid state (list instead of dict)."""
    print("\nTesting planner_agent with invalid state (list)...")
    
    # Create an invalid state (list instead of dict)
    invalid_state = [
        HumanMessage(content="Create a marketing campaign for our new product launch")
    ]
    
    # Create a basic config
    config = RunnableConfig(
        configurable={
            "model": "openai/gpt-3.5-turbo",
            "temperature": 0.7
        }
    )
    
    try:
        result = planner_agent(invalid_state, config)
        print(f"✅ Handled gracefully! Result type: {type(result)}")
        print(f"Result keys: {result.keys()}")
        if "last_error_message" in result:
            print(f"Error message: {result['last_error_message']}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_planner_with_empty_state():
    """Test planner_agent with empty state."""
    print("\nTesting planner_agent with empty state...")
    
    # Create an empty state
    state = {}
    
    # Create a basic config
    config = RunnableConfig(
        configurable={
            "model": "openai/gpt-3.5-turbo",
            "temperature": 0.7
        }
    )
    
    try:
        result = planner_agent(state, config)
        print(f"✅ Handled gracefully! Result type: {type(result)}")
        print(f"Result keys: {result.keys()}")
        if "messages" in result:
            print(f"Message content: {result['messages'][0].content}")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing planner_agent fixes...")
    
    # Set up environment variables if needed
    os.environ.setdefault("OPENAI_API_KEY", "test-key")
    
    results = []
    results.append(test_planner_with_valid_state())
    results.append(test_planner_with_invalid_state())
    results.append(test_planner_with_empty_state())
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All tests passed! The planner_agent should now handle the 'list' object error.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
