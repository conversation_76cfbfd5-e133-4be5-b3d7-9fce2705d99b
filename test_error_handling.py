#!/usr/bin/env python3
"""
Test script to verify the error handling functionality in the supervisor.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agents.bond_ai.graph import error_response_node, supervisor_agent_anthropic
from agents.bond_ai.state import BondAIAgentState
from langchain_core.messages import HumanMessage

def test_error_response_node():
    """Test the error response node functionality."""
    print("Testing error_response_node...")
    
    # Create a test state with an error message
    test_state = {
        "messages": [HumanMessage(content="Test user message")],
        "last_error_message": "Test error: Something went wrong during planning"
    }
    
    # Call the error response node
    result = error_response_node(test_state)
    
    # Verify the result
    assert "messages" in result, "Result should contain messages"
    assert len(result["messages"]) == 1, "Should have exactly one message"
    assert "next" in result, "Result should contain next field"
    assert result["next"] == "FINISH", "Next should be FINISH"
    
    error_message = result["messages"][0].content
    assert "Test error: Something went wrong during planning" in error_message, "Error message should contain the original error"
    assert "I encountered an issue" in error_message, "Should have user-friendly message"
    
    print("✓ error_response_node test passed")
    print(f"Generated message: {error_message}")


def test_supervisor_error_detection():
    """Test that supervisor detects error state and routes to error_response."""
    print("\nTesting supervisor error detection...")
    
    # Create a test state with an error message
    test_state = {
        "messages": [HumanMessage(content="Test user message")],
        "last_error_message": "Planning failed: Invalid input"
    }
    
    # Mock the supervisor_agent_registry to avoid import issues
    import agents.bond_ai.graph as graph_module
    
    # Create a mock registry
    class MockRegistry:
        def get_agent_names(self):
            return ["planner", "research_agent"]
    
    # Temporarily replace the registry
    original_registry = graph_module.supervisor_agent_registry
    graph_module.supervisor_agent_registry = MockRegistry()
    
    try:
        # Call the supervisor
        result = supervisor_agent_anthropic(test_state)
        
        # Verify the result
        assert "next" in result, "Result should contain next field"
        assert result["next"] == "error_response", f"Should route to error_response, got: {result['next']}"
        
        print("✓ supervisor error detection test passed")
        print(f"Supervisor routed to: {result['next']}")
        
    finally:
        # Restore the original registry
        graph_module.supervisor_agent_registry = original_registry


def test_supervisor_normal_operation():
    """Test that supervisor works normally when there's no error."""
    print("\nTesting supervisor normal operation...")
    
    # Create a test state without error message
    test_state = {
        "messages": [HumanMessage(content="Please help me with data analysis")],
        "last_error_message": None  # No error
    }
    
    # Mock the supervisor_agent_registry and LLM
    import agents.bond_ai.graph as graph_module
    
    # Create a mock registry
    class MockRegistry:
        def get_agent_names(self):
            return ["planner", "research_agent"]
        
        def get_react_agents(self):
            return {
                "planner": {"description": "Planning agent", "tools": []},
                "research_agent": {"description": "Research agent", "tools": []}
            }
        
        def get_custom_nodes(self):
            return {}
    
    # Mock LLM response
    class MockLLMResponse:
        def __init__(self, content):
            self.content = content
    
    class MockLLM:
        def invoke(self, messages):
            return MockLLMResponse('{"next": "planner"}')
    
    # Temporarily replace the registry and LLM
    original_registry = graph_module.supervisor_agent_registry
    original_llm = graph_module.llm
    graph_module.supervisor_agent_registry = MockRegistry()
    graph_module.llm = MockLLM()
    
    try:
        # Call the supervisor
        result = supervisor_agent_anthropic(test_state)
        
        # Verify the result
        assert "next" in result, "Result should contain next field"
        assert result["next"] == "planner", f"Should route to planner, got: {result['next']}"
        
        print("✓ supervisor normal operation test passed")
        print(f"Supervisor routed to: {result['next']}")
        
    finally:
        # Restore the original registry and LLM
        graph_module.supervisor_agent_registry = original_registry
        graph_module.llm = original_llm


if __name__ == "__main__":
    print("Running error handling tests...\n")
    
    try:
        test_error_response_node()
        test_supervisor_error_detection()
        test_supervisor_normal_operation()
        
        print("\n🎉 All tests passed! Error handling is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
