#!/usr/bin/env python3
"""
Simple test to verify the planner_agent fix for the 'list' object error.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from langchain_core.messages import HumanMessage

def test_state_type_checking():
    """Test the state type checking logic."""
    print("Testing state type checking...")
    
    # Import the planner agent
    from agents.bond_ai.nodes.planner_node import planner_agent
    
    # Test with invalid state (list instead of dict)
    invalid_state = [
        HumanMessage(content="Test message")
    ]
    
    # Create a minimal config
    from langchain_core.runnables import RunnableConfig
    config = RunnableConfig(configurable={"model": "openai/gpt-3.5-turbo"})
    
    try:
        result = planner_agent(invalid_state, config)
        print(f"✅ Success! The function handled the list state gracefully.")
        print(f"Result type: {type(result)}")
        print(f"Result keys: {list(result.keys())}")
        
        if "last_error_message" in result:
            print(f"Error message: {result['last_error_message']}")
            
        return True
    except Exception as e:
        if "'list' object has no attribute 'get'" in str(e):
            print(f"❌ The original error still occurs: {e}")
            return False
        else:
            print(f"❌ Different error occurred: {e}")
            return False

if __name__ == "__main__":
    print("🧪 Testing planner_agent fix for 'list' object error...")
    
    # Set up environment variables if needed
    os.environ.setdefault("OPENAI_API_KEY", "test-key")
    
    success = test_state_type_checking()
    
    if success:
        print("🎉 Fix verified! The planner_agent now handles the 'list' object error correctly.")
    else:
        print("⚠️  The fix may not be working properly.")
