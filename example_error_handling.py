#!/usr/bin/env python3
"""
Example demonstrating how the supervisor handles error messages.

This example shows:
1. How to set an error message in the state
2. How the supervisor detects and routes to error_response
3. How the error_response node handles the error and ends the conversation
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agents.bond_ai.state import BondAIAgentState
from langchain_core.messages import HumanMessage, AIMessage

def simulate_error_scenario():
    """Simulate a scenario where an error occurs and gets handled."""
    
    print("=== Error Handling Example ===\n")
    
    # Step 1: Initial state with user request
    print("1. User makes a request:")
    initial_state = {
        "messages": [HumanMessage(content="Please analyze the sales data and create a report")],
        "table_summary": "Sales data table with 1000 rows",
        "mode": "analysis",
        "last_error_message": None
    }
    print(f"   User: {initial_state['messages'][0].content}")
    print(f"   Error state: {initial_state['last_error_message']}")
    
    # Step 2: Simulate an error occurring in a node (e.g., planner_node)
    print("\n2. Error occurs in planner node:")
    error_state = initial_state.copy()
    error_state["last_error_message"] = "Planning error: Unable to access database connection"
    error_state["messages"].append(AIMessage(content="I'm analyzing your request..."))
    
    print(f"   Error message set: {error_state['last_error_message']}")
    
    # Step 3: Supervisor detects error and routes to error_response
    print("\n3. Supervisor detects error and routes to error_response:")
    from agents.bond_ai.graph import supervisor_agent_anthropic
    
    # Mock the registry for this example
    import agents.bond_ai.graph as graph_module
    
    class MockRegistry:
        def get_agent_names(self):
            return ["planner", "research_agent", "data_management_agent"]
    
    original_registry = graph_module.supervisor_agent_registry
    graph_module.supervisor_agent_registry = MockRegistry()
    
    try:
        supervisor_result = supervisor_agent_anthropic(error_state)
        print(f"   Supervisor decision: {supervisor_result}")
        
        # Step 4: Error response node handles the error
        print("\n4. Error response node handles the error:")
        from agents.bond_ai.graph import error_response_node
        
        error_response_result = error_response_node(error_state)
        print(f"   Error response: {error_response_result['messages'][0].content}")
        print(f"   Next action: {error_response_result['next']}")
        
        # Step 5: Conversation ends
        print("\n5. Conversation ends gracefully")
        print("   The user receives a helpful error message and the workflow terminates.")
        
    finally:
        graph_module.supervisor_agent_registry = original_registry


def simulate_normal_scenario():
    """Simulate a normal scenario without errors for comparison."""
    
    print("\n\n=== Normal Operation Example (for comparison) ===\n")
    
    # Step 1: Initial state with user request (no error)
    print("1. User makes a request:")
    normal_state = {
        "messages": [HumanMessage(content="Please analyze the sales data and create a report")],
        "table_summary": "Sales data table with 1000 rows",
        "mode": "analysis",
        "last_error_message": None  # No error
    }
    print(f"   User: {normal_state['messages'][0].content}")
    print(f"   Error state: {normal_state['last_error_message']}")
    
    # Step 2: Supervisor routes normally
    print("\n2. Supervisor routes to appropriate agent:")
    from agents.bond_ai.graph import supervisor_agent_anthropic
    
    # Mock the registry and LLM for this example
    import agents.bond_ai.graph as graph_module
    
    class MockRegistry:
        def get_agent_names(self):
            return ["planner", "research_agent", "data_management_agent"]
        
        def get_react_agents(self):
            return {
                "planner": {"description": "Planning and task breakdown", "tools": []},
                "research_agent": {"description": "Research and data gathering", "tools": []},
                "data_management_agent": {"description": "Data processing and analysis", "tools": []}
            }
        
        def get_custom_nodes(self):
            return {}
    
    class MockLLMResponse:
        def __init__(self, content):
            self.content = content
    
    class MockLLM:
        def invoke(self, messages):
            return MockLLMResponse('{"next": "planner"}')
    
    original_registry = graph_module.supervisor_agent_registry
    original_llm = graph_module.llm
    graph_module.supervisor_agent_registry = MockRegistry()
    graph_module.llm = MockLLM()
    
    try:
        supervisor_result = supervisor_agent_anthropic(normal_state)
        print(f"   Supervisor decision: {supervisor_result}")
        print("   Normal workflow continues...")
        
    finally:
        graph_module.supervisor_agent_registry = original_registry
        graph_module.llm = original_llm


def show_implementation_details():
    """Show the key implementation details."""
    
    print("\n\n=== Implementation Details ===\n")
    
    print("1. Error Response Node:")
    print("   - Extracts error message from state.last_error_message")
    print("   - Creates user-friendly error message")
    print("   - Sets next='FINISH' to end conversation")
    
    print("\n2. Supervisor Enhancement:")
    print("   - Checks for state.last_error_message at the beginning")
    print("   - If error exists, immediately routes to 'error_response'")
    print("   - Otherwise, continues with normal routing logic")
    
    print("\n3. Workflow Configuration:")
    print("   - Added 'error_response' node to workflow")
    print("   - Connected error_response -> END")
    print("   - Added 'error_response' to conditional routing map")
    
    print("\n4. Usage in Nodes:")
    print("   - Any node can set last_error_message in return state")
    print("   - Example: return {'last_error_message': 'Database connection failed'}")
    print("   - Supervisor will automatically handle the error on next routing")


if __name__ == "__main__":
    simulate_error_scenario()
    simulate_normal_scenario()
    show_implementation_details()
    
    print("\n🎯 Summary:")
    print("The supervisor now automatically detects errors in the state and")
    print("routes to a dedicated error response node that informs the user")
    print("and gracefully ends the conversation.")
