from datetime import datetime


from bond_ai.models.planner_model import Task
from bond_ai.utils import load_chat_model
from bond_ai.configuration import Configuration
from bond_ai.state import BondAIAgentState
from bond_ai.prompts_v1 import PLANNER_AGENT_PROMPT
from langchain_core.messages import SystemMessage,HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from bond_ai.tools import all_tools


# def _generate_plan_markdown(
#     current_plan: List[Dict[str, Any]],
# ) -> str:
#     """Generate Markdown content for the plan.

#     Args:
#         current_plan: List of task dictionaries
#         progress_metrics: Progress tracking metrics
#         active_task_id: ID of currently active task
#         user_context: User context information
#         session_config: Session configuration

#     Returns:
#         Formatted Markdown string
#     """

#     lines = []

#     # Header
#     lines.append("# CatchUp Agent Plan")
#     lines.append("")
#     lines.append(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
#     lines.append("")

#     lines.append("")

#     # Tasks
#     lines.append("## Tasks")
#     lines.append("")

#     for i, task in enumerate(current_plan, 1):
#         # Status emoji and indicators
#         status_emoji = {
#             "pending": "⏳",
#             "in_progress": "🔄",
#             "completed": "✅",
#             "failed": "❌"
#         }.get(task.get("status", "pending"), "❓")

      
  

#         # Task header
#         lines.append("")

#         # Task details
#         lines.append("**Details:**")
#         lines.append(f"- **ID:** `{task.get('id', 'N/A')}`")
#         lines.append(f"- **Status:** {task.get('status', 'pending')}")
#         lines.append(f"- **Priority:** {task.get('priority', 'medium')}")

#         if task.get("estimated_duration"):
#             lines.append(f"- **Estimated Duration:** {task['estimated_duration']} seconds")

#         if task.get("actual_duration"):
#             lines.append(f"- **Actual Duration:** {task['actual_duration']} seconds")

#         if task.get("tools_required"):
#             tools_list = ", ".join(f"`{tool}`" for tool in task["tools_required"])
#             lines.append(f"- **Tools Required:** {tools_list}")

#         if task.get("dependencies"):
#             deps_list = ", ".join(f"`{dep}`" for dep in task["dependencies"])
#             lines.append(f"- **Dependencies:** {deps_list}")

#         if task.get("created_at"):
#             lines.append(f"- **Created:** {task['created_at']}")

#         if task.get("started_at"):
#             lines.append(f"- **Started:** {task['started_at']}")

#         if task.get("completed_at"):
#             lines.append(f"- **Completed:** {task['completed_at']}")

#         if task.get("error_message"):
#             lines.append(f"- **Error:** {task['error_message']}")

#         lines.append("")



#     # Footer
#     lines.append("---")
#     lines.append("*Generated by CatchUp Agent v1 - Testing Utility*")

#     return "\n".join(lines)

# async def _save_plan_automatically(
#     plan_tasks: List[Dict[str, Any]],


# ) -> None:
#     """Automatically save plan to markdown file for testing purposes.

#     Args:
#         plan_tasks: List of task dictionaries
#         progress_metrics: Progress tracking metrics
#         user_context: User context information
#         session_config: Session configuration
#     """

#     try:
#         # Create directory if it doesn't exist (using asyncio.to_thread for blocking operation)
#         plan_dir = Path("plans")
#         await asyncio.to_thread(plan_dir.mkdir, exist_ok=True)

#         # Generate filename
#         timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#         filename = f"plan_{timestamp}.md"

#         file_path = plan_dir / filename

#         # Generate Markdown content
#         markdown_content = _generate_plan_markdown(
#             plan_tasks
       
       
       
#         )

#         # Write to file using aiofiles for async file operations
#         async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
#             await f.write(markdown_content)

#         print(f"✅ Plan automatically saved to: {file_path.absolute()}")

#     except Exception as e:
#         print(f"❌ Failed to auto-save plan: {e}")
#         raise


def planner_agent(
    state: BondAIAgentState,
    config: RunnableConfig,
):
    """Planning node that analyzes user requests and creates comprehensive plans.

    This node:
    1. Analyzes the user's request to understand intent and complexity
    2. Creates a detailed plan with specific, actionable tasks
    3. Estimates task durations and identifies required tools
    4. Sets up dependencies between tasks
    5. Updates the conversation phase to planning
    """
    # Get configuration
    configuration = Configuration.from_runnable_config(config)

    # Add type checking and debugging for state parameter
    if not isinstance(state, dict):
        print(f"ERROR: state is not a dict, it's a {type(state)}: {state}")
        return {
            "messages": [AIMessage(content="Internal error: Invalid state format.")],
            "last_error_message": f"State type error: expected dict, got {type(state)}"
        }

    messages = state.get("messages", [])
  
    #TODO: EdgeCase: handle the case where there are no messages  
    
    if not messages:
        return {"messages": [AIMessage(content="No messages to plan for.")]}    
    # Get the latest user message
    user_message = None
    for msg in reversed(messages):
        if hasattr(msg, 'type') and msg.type == "human":
            user_message = msg
            break
    
    if not user_message:
        return {"messages": [AIMessage(content="No user message found to create a plan for.")]}
  
    # Create planning prompt using direct message construction to avoid template issues
    system_message = SystemMessage(content=PLANNER_AGENT_PROMPT)
    
    
    human_content = f"""
User Request: {user_message.content}



Create a detailed plan to fulfill this request. Break it down into specific, actionable tasks.
Each task should be atomic and executable independently where possible.
Consider tool availability and user context when creating tasks.
"""
    human_message_obj = HumanMessage(content=human_content)
    messages = [system_message, human_message_obj]
    
    llm = load_chat_model(configuration.model)
    

    
    try:
        # Generate plan using structured output (KO) Current LLM "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0" with thinking does not support structure data output. then we need to parse the output
        plan_response = llm.invoke(messages)
 
        
        # Parse the response content to extract JSON
        tasks_data = []

        # Handle different response formats
        if hasattr(plan_response, 'content'):
            content = plan_response.content

            # Case 1: content is a list (structured response)
            if isinstance(content, list):
                for item in content:
                    if isinstance(item, dict) and item.get('type') == 'text':
                        text_content = item.get('text', '')

                        # Look for JSON in the text content
                        if '```json' in text_content:
                            try:
                                # Extract JSON from markdown code block
                                json_start = text_content.find('```json') + 7
                                json_end = text_content.find('```', json_start)
                                json_str = text_content[json_start:json_end].strip()

                                import json
                                plan_data = json.loads(json_str)
                                tasks_data = plan_data.get('tasks', [])
                                break
                            except (json.JSONDecodeError, ValueError) as e:
                                continue

            # Case 2: content is a string (simple response)
            elif isinstance(content, str):
                if '```json' in content:
                    try:
                        # Extract JSON from markdown code block
                        json_start = content.find('```json') + 7
                        json_end = content.find('```', json_start)
                        json_str = content[json_start:json_end].strip()

                        import json
                        plan_data = json.loads(json_str)
                        tasks_data = plan_data.get('tasks', [])
                    except (json.JSONDecodeError, ValueError) as e:
                        pass
    
        
       
        if not tasks_data:
            return {
                "messages": [AIMessage(content="I couldn't create a specific plan for your request. Let me help you directly.")]
            }
        
        # Convert to Task objects
        plan_tasks = []
        
        for i, task_data in enumerate(tasks_data):
            # Add type checking for task_data
            if not isinstance(task_data, dict):
                print(f"WARNING: task_data at index {i} is not a dict, it's a {type(task_data)}: {task_data}")
                # Try to convert if it's a list with expected structure
                if isinstance(task_data, list) and len(task_data) >= 6:
                    # Assume order: [id, order, action, agent, tool, why]
                    task_data = {
                        "id": task_data[0] if len(task_data) > 0 else f"task_{i+1}",
                        "order": task_data[1] if len(task_data) > 1 else i + 1,
                        "action": task_data[2] if len(task_data) > 2 else "",
                        "agent": task_data[3] if len(task_data) > 3 else "",
                        "tool": task_data[4] if len(task_data) > 4 else "",
                        "why": task_data[5] if len(task_data) > 5 else "",
                    }
                else:
                    # Create a default task if we can't parse the data
                    task_data = {
                        "id": f"task_{i+1}",
                        "order": i + 1,
                        "action": f"Process item {i+1}",
                        "agent": "general",
                        "tool": "unknown",
                        "why": "Unable to parse task data",
                    }

            task = Task(
                #id=f"task_{i}_{int(datetime.now().timestamp())}",
                id = task_data.get("id", f"task_{i+1}"),
                order=task_data.get("order", i + 1),
                action=task_data.get("action", ""),
                agent=task_data.get("agent", ""),
                tool=task_data.get("tool", ""),
                why=task_data.get("why", ""),
                status="pending",
                error=None
            )
            plan_tasks.append(task)
        
     
        
        return {
            "plan_tasks": plan_tasks,
        }

    except Exception as e:

        return {
            "plan_tasks": [],
            "messages": [AIMessage(content=f"I encountered an issue while planning. Let me assist you directly.")],
            "last_error_message": f"Planning error: {str(e)}",
            # "conversation_phase": {
            #     "phase": "understanding",
            #     "confidence": 0.5,
            #     "context": {"planning_error": str(e)}
            # },
            # "error_context": {
            #     "node": "planner",
            #     "error": str(e),
            #     "user_request": user_message.content
            # }
        }


